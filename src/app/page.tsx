'use client';

import { useRouter } from 'next/navigation';
import { AuthLayout } from '@/shared/components/layout';
import { But<PERSON> } from '@/shared/components/ui';

export default function Home() {
  const router = useRouter();

  const handleCreateAccount = () => {
    router.push('/auth/signup');
  };

  const handleLogin = () => {
    router.push('/auth/login');
  };

  return (
    <AuthLayout variant="home">
      <div className="space-y-4 mt-8">
        <Button
          onClick={handleCreateAccount}
          className="w-full"
          size="lg"
        >
          Create an Account
        </Button>

        <Button
          onClick={handleLogin}
          variant="outline"
          className="w-full"
          size="lg"
        >
          Login
        </Button>
      </div>
    </AuthLayout>
  );
}
