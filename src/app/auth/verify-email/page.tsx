'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { AuthLayout } from '@/shared/components/layout';
import { VerifyEmail } from '@/features/auth';

export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const userType = searchParams.get('type') || 'client';

  const handleBack = () => {
    router.push('/auth/signup');
  };

  const handleVerificationSuccess = () => {
    // After successful signup, direct all user types to /dashboard
    router.push('/dashboard');
  };

  return (
    <AuthLayout
      title="Verify Email"
      showBackButton={true}
      onBack={handleBack}
      contentType="simple"
    >
      <VerifyEmail onSuccess={handleVerificationSuccess} />
    </AuthLayout>
  );
}
