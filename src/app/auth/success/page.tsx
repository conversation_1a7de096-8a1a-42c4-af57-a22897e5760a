'use client';

import { useRouter } from 'next/navigation';
import { AuthLayout } from '@/shared/components/layout';
import { Button } from '@/shared/components/ui';
import { CheckIcon } from '@/shared/components/icons';

export default function SuccessPage() {
  const router = useRouter();

  const handleContinue = () => {
    router.push('/auth/login');
  };

  return (
    <AuthLayout title="Success">
      <div className="text-center space-y-6">
        {/* Success Icon */}
        <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
          <CheckIcon className="text-green-600" size="xl" />
        </div>
        
        {/* Title */}
        <h2 className="text-2xl font-bold text-gray-900">
          Password Changed!
        </h2>
        
        {/* Message */}
        <p className="text-gray-600">
          Your password has been successfully changed. You can now login with your new password.
        </p>
        
        {/* Button */}
        <Button
          onClick={handleContinue}
          className="w-full"
          size="lg"
        >
          Login Now
        </Button>
      </div>
    </AuthLayout>
  );
}
