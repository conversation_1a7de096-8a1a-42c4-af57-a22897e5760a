'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AuthLayout } from '@/shared/components/layout';
import { Button, OTPInput } from '@/shared/components/ui';
import { useForgotPassword } from '@/features/auth/hooks/use-forgot-password';
import { useResendTimer } from '@/features/auth/hooks/use-resend-timer';

export default function OTPPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [otp, setOtp] = useState('');
  const [email, setEmail] = useState('');

  const { resendPasswordResetOTP, isLoading, error, success, clearMessages } = useForgotPassword();
  const { timeLeft, canResend, startTimer } = useResendTimer(60);

  // Get email from URL params
  useEffect(() => {
    const emailParam = searchParams.get('email');
    if (emailParam) {
      setEmail(emailParam);
    } else {
      // If no email, redirect back to forgot password
      router.push('/auth/forgot-password');
    }
  }, [searchParams, router]);

  // Start timer on component mount
  useEffect(() => {
    startTimer(60);
  }, [startTimer]);

  const handleOTPComplete = (otpValue: string) => {
    setOtp(otpValue);
    // Clear any existing messages
    if (error || success) {
      clearMessages();
    }
  };

  const handleVerify = () => {
    if (otp.length === 6 && email) {
      // Navigate to reset password page with email and OTP
      router.push(`/auth/reset?email=${encodeURIComponent(email)}&otp=${otp}`);
    }
  };

  const handleResend = async () => {
    if (!canResend || !email) return;

    try {
      await resendPasswordResetOTP(email);
      startTimer(60); // Start 60 second timer
    } catch (err) {
      console.error('Failed to resend OTP:', err);
    }
  };

  const handleBack = () => {
    router.push('/auth/forgot-password');
  };

  return (
    <AuthLayout
      title="Enter OTP"
      showBackButton={true}
      onBack={handleBack}
      contentType="simple"
    >
      <div className="space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md text-sm">
            {success}
          </div>
        )}

        <div className="text-center">
          <div className="mb-4">
            <p className="text-sm text-gray-600">
              Enter the 6-digit OTP sent to
            </p>
            <p className="text-sm font-medium text-gray-900">
              {email}
            </p>
          </div>

          <OTPInput
            length={6}
            onComplete={handleOTPComplete}
            onChange={setOtp}
          />
        </div>

        <Button 
          onClick={handleVerify} 
          className="w-full" 
          size="lg"
          disabled={otp.length !== 6}
        >
          Verify
        </Button>

        <div className="text-center">
          <span className="text-gray-600 text-sm">
            Haven't received OTP?{' '}
            {canResend ? (
              <button
                type="button"
                onClick={handleResend}
                disabled={isLoading}
                className="text-amber-600 hover:text-amber-700 font-medium disabled:opacity-50"
              >
                {isLoading ? 'Sending...' : 'Resend'}
              </button>
            ) : (
              <span className="text-gray-500">
                Resend in {timeLeft}s
              </span>
            )}
          </span>
        </div>
      </div>
    </AuthLayout>
  );
}
