/**
 * Validation constants including regex patterns, magic numbers, and configuration values
 */

// Regex patterns for validation
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  URL: /^https?:\/\/.+/,
  NUMERIC: /^\d+(\.\d+)?$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
  ZIP_CODE: /^\d{5}(-\d{4})?$/,
  // Additional patterns for future use
  ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
  LETTERS_ONLY: /^[a-zA-Z\s]+$/,
  NUMBERS_ONLY: /^\d+$/,
} as const;

// File size constants (in bytes)
export const FILE_SIZE_LIMITS = {
  BYTES_PER_KB: 1024,
  BYTES_PER_MB: 1024 * 1024,
  BYTES_PER_GB: 1024 * 1024 * 1024,
  
  // Specific file size limits
  BARBER_CERTIFICATE_MAX: 5 * 1024 * 1024, // 5MB
  PROFILE_IMAGE_MAX: 2 * 1024 * 1024, // 2MB
  DOCUMENT_MAX: 10 * 1024 * 1024, // 10MB
} as const;

// Validation length constraints
export const VALIDATION_LENGTHS = {
  PASSWORD_MIN: 8,
  PASSWORD_MAX: 128,
  NAME_MIN: 2,
  NAME_MAX: 50,
  ADDRESS_STREET_MIN: 5,
  ADDRESS_STREET_MAX: 100,
  ADDRESS_CITY_MIN: 2,
  ADDRESS_CITY_MAX: 50,
  ADDRESS_STATE_MIN: 2,
  ADDRESS_STATE_MAX: 50,
  PHONE_MIN: 10,
  PHONE_MAX: 15,
  EMAIL_MAX: 254,
} as const;

// Password strength scoring
export const PASSWORD_STRENGTH = {
  MIN_SCORE: 0,
  MAX_SCORE: 5,
  WEAK_THRESHOLD: 2,
  MEDIUM_THRESHOLD: 3,
  STRONG_THRESHOLD: 4,
} as const;

// File validation constants
export const FILE_VALIDATION = {
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  ALLOWED_CERTIFICATE_TYPES: ['application/pdf', 'image/jpeg', 'image/png'],
  
  ALLOWED_IMAGE_EXTENSIONS: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  ALLOWED_DOCUMENT_EXTENSIONS: ['pdf', 'doc', 'docx'],
  ALLOWED_CERTIFICATE_EXTENSIONS: ['pdf', 'jpg', 'jpeg', 'png'],
} as const;

// Validation error message templates
export const VALIDATION_ERROR_TEMPLATES = {
  REQUIRED: (fieldName: string) => `${fieldName} is required`,
  MIN_LENGTH: (fieldName: string, minLength: number) => `${fieldName} must be at least ${minLength} characters long`,
  MAX_LENGTH: (fieldName: string, maxLength: number) => `${fieldName} must be no more than ${maxLength} characters long`,
  INVALID_FORMAT: (fieldName: string) => `${fieldName} format is invalid`,
  INVALID_URL: (fieldName: string) => `${fieldName} must be a valid URL`,
  INVALID_NUMBER: (fieldName: string) => `${fieldName} must be a valid number`,
  MIN_VALUE: (fieldName: string, minValue: number) => `${fieldName} must be at least ${minValue}`,
  MAX_VALUE: (fieldName: string, maxValue: number) => `${fieldName} must be no more than ${maxValue}`,
  FILE_TOO_LARGE: (maxSizeMB: number) => `File size must be less than ${maxSizeMB}MB`,
  FILE_TYPE_NOT_ALLOWED: (fileType: string) => `File type ${fileType} is not allowed`,
  FILE_EXTENSION_NOT_ALLOWED: (allowedExtensions: string[]) => `File extension must be one of: ${allowedExtensions.join(', ')}`,
} as const;

// Common validation configurations
export const VALIDATION_CONFIGS = {
  BARBER_CERTIFICATE: {
    maxSize: FILE_SIZE_LIMITS.BARBER_CERTIFICATE_MAX,
    allowedTypes: FILE_VALIDATION.ALLOWED_CERTIFICATE_TYPES,
    allowedExtensions: FILE_VALIDATION.ALLOWED_CERTIFICATE_EXTENSIONS,
  },
  PROFILE_IMAGE: {
    maxSize: FILE_SIZE_LIMITS.PROFILE_IMAGE_MAX,
    allowedTypes: FILE_VALIDATION.ALLOWED_IMAGE_TYPES,
    allowedExtensions: FILE_VALIDATION.ALLOWED_IMAGE_EXTENSIONS,
  },
  DOCUMENT: {
    maxSize: FILE_SIZE_LIMITS.DOCUMENT_MAX,
    allowedTypes: FILE_VALIDATION.ALLOWED_DOCUMENT_TYPES,
    allowedExtensions: FILE_VALIDATION.ALLOWED_DOCUMENT_EXTENSIONS,
  },
} as const;
