/**
 * Reusable icon components to eliminate SVG duplication
 * All icons use consistent sizing and styling patterns
 */
import React from 'react';
import { cn } from '@/lib/utils';

export interface IconProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-10 h-10',
};

/**
 * Success/Check icon - used in success modals, file uploads, etc.
 */
export const CheckIcon: React.FC<IconProps> = ({ className, size = 'lg' }) => (
  <svg
    className={cn(sizeClasses[size], className)}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M5 13l4 4L19 7"
    />
  </svg>
);

/**
 * Check circle icon - used for completed states
 */
export const CheckCircleIcon: React.FC<IconProps> = ({ className, size = 'lg' }) => (
  <svg
    className={cn(sizeClasses[size], className)}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
    />
  </svg>
);

/**
 * Upload icon - used in file upload components
 */
export const UploadIcon: React.FC<IconProps> = ({ className, size = 'lg' }) => (
  <svg
    className={cn(sizeClasses[size], className)}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
    />
  </svg>
);

/**
 * User icon - used for client signup tabs
 */
export const UserIcon: React.FC<IconProps> = ({ className, size = 'lg' }) => (
  <svg
    className={cn(sizeClasses[size], className)}
    fill="currentColor"
    viewBox="0 0 24 24"
  >
    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
  </svg>
);

/**
 * Barber/Scissors icon - used for barber signup tabs
 */
export const BarberIcon: React.FC<IconProps> = ({ className, size = 'lg' }) => (
  <svg
    className={cn(sizeClasses[size], className)}
    fill="currentColor"
    viewBox="0 0 24 24"
  >
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
  </svg>
);

/**
 * Shop/Store icon - used for shop owner signup tabs
 */
export const ShopIcon: React.FC<IconProps> = ({ className, size = 'lg' }) => (
  <svg
    className={cn(sizeClasses[size], className)}
    fill="currentColor"
    viewBox="0 0 24 24"
  >
    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
  </svg>
);

/**
 * Eye icon - used for password visibility toggle
 */
export const EyeIcon: React.FC<IconProps> = ({ className, size = 'md' }) => (
  <svg
    className={cn(sizeClasses[size], className)}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
    />
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
    />
  </svg>
);

/**
 * Eye off icon - used for password visibility toggle
 */
export const EyeOffIcon: React.FC<IconProps> = ({ className, size = 'md' }) => (
  <svg
    className={cn(sizeClasses[size], className)}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
    />
  </svg>
);

/**
 * Loading spinner icon
 */
export const SpinnerIcon: React.FC<IconProps> = ({ className, size = 'md' }) => (
  <svg
    className={cn(sizeClasses[size], 'animate-spin', className)}
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    />
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    />
  </svg>
);

/**
 * Error/X icon - used for error states
 */
export const XIcon: React.FC<IconProps> = ({ className, size = 'md' }) => (
  <svg
    className={cn(sizeClasses[size], className)}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M6 18L18 6M6 6l12 12"
    />
  </svg>
);

/**
 * Warning icon - used for warning states
 */
export const WarningIcon: React.FC<IconProps> = ({ className, size = 'md' }) => (
  <svg
    className={cn(sizeClasses[size], className)}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
    />
  </svg>
);

/**
 * Info icon - used for informational states
 */
export const InfoIcon: React.FC<IconProps> = ({ className, size = 'md' }) => (
  <svg
    className={cn(sizeClasses[size], className)}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
    />
  </svg>
);
