'use client';

import { cn } from '@/lib/utils';

interface OptionButtonProps {
  children: React.ReactNode;
  selected?: boolean;
  onClick?: () => void;
  variant?: 'default' | 'primary';
  className?: string;
}

export function OptionButton({ 
  children, 
  selected = false, 
  onClick, 
  variant = 'default',
  className 
}: OptionButtonProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        'px-4 py-2 rounded-full text-sm font-medium transition-all duration-200',
        'border border-gray-300 hover:border-gray-400',
        variant === 'primary' && selected && 'bg-amber-800 text-white border-amber-800',
        variant === 'default' && selected && 'bg-gray-900 text-white border-gray-900',
        !selected && 'bg-white text-gray-700 hover:bg-gray-50',
        className
      )}
    >
      {children}
    </button>
  );
}
