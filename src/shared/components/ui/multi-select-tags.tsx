'use client';

import { useState } from 'react';
import { cn } from '@/lib/utils';
import { OptionButton } from './option-button';
import { Input } from './input';

interface MultiSelectTagsProps {
  options: string[];
  selectedValues: string[];
  onChange: (values: string[]) => void;
  label?: string;
  placeholder?: string;
  allowCustom?: boolean;
  customLabel?: string;
  className?: string;
  variant?: 'default' | 'primary';
}

export function MultiSelectTags({
  options,
  selectedValues,
  onChange,
  label,
  placeholder = "Select options",
  allowCustom = false,
  customLabel = "Other",
  className,
  variant = 'primary',
}: MultiSelectTagsProps) {
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [customValue, setCustomValue] = useState('');

  // Check if "Other" is selected
  const isOtherSelected = selectedValues.some(value => !options.includes(value));

  const handleOptionToggle = (option: string) => {
    const newSelection = selectedValues.includes(option)
      ? selectedValues.filter(item => item !== option)
      : [...selectedValues, option];
    
    onChange(newSelection);
  };

  const handleOtherToggle = () => {
    if (isOtherSelected) {
      // Remove all custom values (values not in predefined options)
      const newSelection = selectedValues.filter(value => options.includes(value));
      onChange(newSelection);
      setShowCustomInput(false);
      setCustomValue('');
    } else {
      setShowCustomInput(true);
    }
  };

  const handleCustomSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (customValue.trim()) {
      const newSelection = [...selectedValues.filter(value => options.includes(value)), customValue.trim()];
      onChange(newSelection);
      setCustomValue('');
      setShowCustomInput(false);
    }
  };

  const handleCustomCancel = () => {
    setShowCustomInput(false);
    setCustomValue('');
  };

  const handleRemoveCustomTag = (customTag: string) => {
    const newSelection = selectedValues.filter(value => value !== customTag);
    onChange(newSelection);
  };

  // Get custom tags (values not in predefined options)
  const customTags = selectedValues.filter(value => !options.includes(value));

  return (
    <div className={cn('space-y-4', className)}>
      {label && (
        <h3 className="text-lg font-semibold text-gray-900">{label}</h3>
      )}
      
      <div className="space-y-4">
        {/* Predefined Options */}
        <div className="flex flex-wrap gap-3">
          {options.map((option) => (
            <OptionButton
              key={option}
              selected={selectedValues.includes(option)}
              onClick={() => handleOptionToggle(option)}
              variant={variant}
            >
              {option}
            </OptionButton>
          ))}
          
          {/* Other Option */}
          {allowCustom && (
            <OptionButton
              selected={isOtherSelected}
              onClick={handleOtherToggle}
              variant={variant}
            >
              {customLabel}
            </OptionButton>
          )}
        </div>

        {/* Custom Tags Display */}
        {customTags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {customTags.map((tag) => (
              <div
                key={tag}
                className="inline-flex items-center gap-2 px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm font-medium"
              >
                <span>{tag}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveCustomTag(tag)}
                  className="text-amber-600 hover:text-amber-800 transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Custom Input */}
        {showCustomInput && (
          <form onSubmit={handleCustomSubmit} className="space-y-3">
            <Input
              type="text"
              value={customValue}
              onChange={(e) => setCustomValue(e.target.value)}
              placeholder="Enter your custom option"
              className="max-w-sm"
              autoFocus
            />
            <div className="flex gap-2">
              <button
                type="submit"
                disabled={!customValue.trim()}
                className="px-4 py-2 bg-amber-600 text-white rounded-lg text-sm font-medium hover:bg-amber-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Add
              </button>
              <button
                type="button"
                onClick={handleCustomCancel}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}