import React, { useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { CheckCircleIcon, UploadIcon } from '@/shared/components/icons';

interface FileUploadProps {
  label?: string;
  placeholder?: string;
  accept?: string;
  onChange?: (file: File | null) => void;
  className?: string;
  error?: string;
  required?: boolean;
}

export function FileUpload({
  label,
  placeholder = "Upload your file",
  accept,
  onChange,
  className,
  error,
  required = false,
}: FileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File | null) => {
    setSelectedFile(file);
    onChange?.(file);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    handleFileSelect(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const file = e.dataTransfer.files?.[0] || null;
    handleFileSelect(file);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleClick = (e: React.MouseEvent) => {
    // Prevent double triggering if the hidden input is clicked
    if (e.target === fileInputRef.current) {
      return;
    }
    e.preventDefault();
    fileInputRef.current?.click();
  };

  const handleRemoveFile = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleFileSelect(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div
        onClick={handleClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={cn(
          'relative border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors',
          'hover:border-amber-400 hover:bg-amber-50',
          isDragOver ? 'border-amber-400 bg-amber-50' : 'border-gray-300',
          error ? 'border-red-300' : '',
          'focus-within:ring-2 focus-within:ring-amber-500 focus-within:border-amber-500'
        )}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileInputChange}
          className="sr-only"
        />
        
        <div className="text-center">
          {selectedFile ? (
            <div className="space-y-2">
              <div className="flex items-center justify-center">
                <CheckCircleIcon className="text-green-500" size="lg" />
              </div>
              <div className="text-sm text-gray-600">
                <span className="font-medium">{selectedFile.name}</span>
                <span className="text-gray-400 ml-2">
                  ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                </span>
              </div>
              <button
                type="button"
                onClick={handleRemoveFile}
                className="text-xs text-red-500 hover:text-red-700"
              >
                Remove file
              </button>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center justify-center">
                <UploadIcon className="text-gray-400" size="lg" />
              </div>
              <div className="text-sm text-gray-600">
                <span className="font-medium text-amber-600">Click to upload</span>
                <span className="text-gray-400"> or drag and drop</span>
              </div>
              <p className="text-xs text-gray-400">{placeholder}</p>
            </div>
          )}
        </div>
      </div>
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}