import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps {
  options: SelectOption[];
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  label?: string;
  required?: boolean;
  error?: string;
  className?: string;
  icon?: React.ReactNode;
}

export function Select({
  options,
  value,
  onChange,
  placeholder = "Select an option",
  label,
  required = false,
  error,
  className,
  icon,
}: SelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<SelectOption | null>(
    options.find(option => option.value === value) || null
  );
  const selectRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    const option = options.find(option => option.value === value);
    setSelectedOption(option || null);
  }, [value, options]);

  const handleOptionSelect = (option: SelectOption) => {
    setSelectedOption(option);
    onChange?.(option.value);
    setIsOpen(false);
  };

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative" ref={selectRef}>
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className={cn(
            'relative w-full bg-white border rounded-lg px-4 py-3 text-left cursor-pointer',
            'focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500',
            'transition-colors duration-200',
            error ? 'border-red-300' : 'border-gray-300 hover:border-gray-400',
            'flex items-center gap-3'
          )}
        >
          {icon && (
            <div className="text-gray-400 flex-shrink-0">
              {icon}
            </div>
          )}
          
          <span className={cn(
            'block truncate flex-1',
            selectedOption ? 'text-gray-900' : 'text-gray-400'
          )}>
            {selectedOption ? selectedOption.label : placeholder}
          </span>
          
          <div className="flex-shrink-0">
            <svg
              className={cn(
                'w-5 h-5 text-gray-400 transition-transform duration-200',
                isOpen ? 'transform rotate-180' : ''
              )}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </button>

        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto">
            {options.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => handleOptionSelect(option)}
                className={cn(
                  'w-full px-4 py-3 text-left hover:bg-amber-50 focus:bg-amber-50',
                  'focus:outline-none transition-colors duration-150',
                  selectedOption?.value === option.value ? 'bg-amber-50 text-amber-900' : 'text-gray-900'
                )}
              >
                {option.label}
              </button>
            ))}
          </div>
        )}
      </div>
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}
