// Auth feature constants - All auth-specific constants in one place

// Auth route constants
export const AUTH_ROUTES = {
  // Authentication pages
  HOME: '/',
  LOGIN: '/auth/login',
  SIGNUP: '/auth/signup',
  VERIFY_EMAIL: '/auth/verify-email',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  SUCCESS: '/auth/success',
  
  // Post-auth flow
  WELCOME: '/auth/welcome',
  
  // App routes
  DASHBOARD: '/dashboard',
} as const;

// Auth form validation messages
export const AUTH_MESSAGES = {
  // Success messages
  LOGIN_SUCCESS: 'Login successful',
  REGISTRATION_SUCCESS: 'Registration successful',
  EMAIL_VERIFIED: 'Email verified successfully',
  PASSWORD_RESET_SUCCESS: 'Password reset successfully',
  
  // Error messages
  INVALID_CREDENTIALS: 'Invalid email or password',
  EMAIL_NOT_VERIFIED: 'Please verify your email before logging in',
  EMAIL_ALREADY_EXISTS: 'An account with this email already exists',
  WEAK_PASSWORD: 'Password must be at least 8 characters long',
  PASSWORD_MISMATCH: 'Passwords do not match',
  INVALID_OTP: 'Invalid or expired OTP',
  
  // Loading messages
  LOGGING_IN: 'Logging you in...',
  CREATING_ACCOUNT: 'Creating your account...',
  SENDING_OTP: 'Sending verification code...',
  VERIFYING_EMAIL: 'Verifying your email...',
  RESETTING_PASSWORD: 'Resetting your password...',
} as const;

// Auth form field names (for consistency)
export const AUTH_FIELDS = {
  EMAIL: 'email',
  PASSWORD: 'password',
  CONFIRM_PASSWORD: 'confirmPassword',
  FIRST_NAME: 'firstName',
  LAST_NAME: 'lastName',
  PHONE: 'phone',
  ROLE: 'role',
  EXPERIENCE: 'experience',
  VAT_NUMBER: 'vatNumber',
  BUSINESS_NAME: 'businessName',
  OTP: 'otp',
} as const;

// Role-specific constants
export const USER_ROLES = {
  CLIENT: 'client' as const,
  BARBER: 'barber' as const,
  SHOP_OWNER: 'shop-owner' as const,
};

// Auth-specific validation rules
export const AUTH_VALIDATION_RULES = {
  PASSWORD_MIN_LENGTH: 8,
  OTP_LENGTH: 6,
  PHONE_MIN_LENGTH: 10,
  NAME_MIN_LENGTH: 2,
  VAT_NUMBER_MIN_LENGTH: 8,
  EMAIL: {
    REQUIRED: 'Email is required',
    INVALID: 'Please enter a valid email address',
  },
  PASSWORD: {
    REQUIRED: 'Password is required',
    MIN_LENGTH: 'Password must be at least 8 characters long',
    PATTERN: 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  },
  FIRST_NAME: {
    REQUIRED: 'First name is required',
    MIN_LENGTH: 'First name must be at least 2 characters long',
  },
  LAST_NAME: {
    REQUIRED: 'Last name is required',
    MIN_LENGTH: 'Last name must be at least 2 characters long',
  },
  PHONE_NUMBER: {
    REQUIRED: 'Phone number is required',
    INVALID: 'Please enter a valid phone number',
  },
  VAT_NUMBER: {
    REQUIRED: 'VAT number is required',
    INVALID: 'Please enter a valid VAT number',
  },
  ADDRESS: {
    STREET_REQUIRED: 'Street address is required',
    CITY_REQUIRED: 'City is required',
    STATE_REQUIRED: 'State is required',
    ZIPCODE_REQUIRED: 'ZIP code is required',
    ZIPCODE_INVALID: 'Please enter a valid ZIP code',
  },
  FILE: {
    REQUIRED: 'File is required',
    INVALID_TYPE: 'Invalid file type',
    TOO_LARGE: 'File size is too large',
  },
} as const;

// Auth-related timeouts and intervals
export const AUTH_CONFIG = {
  TOKEN_REFRESH_INTERVAL: 5 * 60 * 1000, // 5 minutes
  OTP_EXPIRY_TIME: 10 * 60 * 1000, // 10 minutes
  AUTO_LOGOUT_TIME: 30 * 60 * 1000, // 30 minutes
  PASSWORD_RESET_TIMEOUT: 15 * 60 * 1000, // 15 minutes
} as const;

// Signup tabs configuration
export const SIGNUP_TABS_CONFIG = [
  {
    id: 'client' as const,
    label: 'Client',
  },
  {
    id: 'barber' as const,
    label: 'Barber',
  },
  {
    id: 'shop-owner' as const,
    label: 'Shop Owner',
  },
];

// OTP configuration
export const OTP_LENGTH = 6;
export const OTP_RESEND_TIMEOUT = 60; // seconds
