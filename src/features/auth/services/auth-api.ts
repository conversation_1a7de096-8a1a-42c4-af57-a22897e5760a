// Authentication API service
import {
  LoginFormData,
  AuthResponse,
  LoginApiResponse,
  User,
  UserRole,
  ClientFormData,
  BarberFormData,
  ShopOwnerFormData,
  VerifyEmailRequest,
  ResetPasswordRequest
} from '@/types/auth';
import { ApiResponse } from '@/types/api';
import { API_CONFIG, STORAGE_KEYS } from '@/shared/constants';

class AuthApiService {
  private baseUrl = API_CONFIG.BASE_URL;

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const token = this.getStoredToken();

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Request failed');
      }

      return data;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  private getStoredToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  }

  private storeTokens(authResponse: AuthResponse): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, authResponse.accessToken);
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, authResponse.refreshToken);
    localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(authResponse.user));
  }

  private clearTokens(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER_DATA);
  }

  async login(credentials: LoginFormData): Promise<AuthResponse | { requiresEmailVerification: boolean; user: any; message: string }> {
    const response = await fetch(`${this.baseUrl}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    const data: LoginApiResponse = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Login failed');
    }

    if (data.success && data.data) {
      // Check if email verification is required by checking if the property exists
      if ('requiresEmailVerification' in data.data && data.data.requiresEmailVerification) {
        return {
          requiresEmailVerification: true,
          user: data.data.user,
          message: data.message || 'Please verify your email address'
        };
      }

      // For successful login with tokens, check if tokens exist
      if ('accessToken' in data.data && 'refreshToken' in data.data) {
        const authResponse: AuthResponse = {
          user: data.data.user as User,
          accessToken: data.data.accessToken as string,
          refreshToken: data.data.refreshToken as string,
        };

        this.storeTokens(authResponse);
        return authResponse;
      }
    }

    throw new Error(data.message || 'Login failed');
  }

  async register(
    userData: ClientFormData | BarberFormData | ShopOwnerFormData, 
    role: UserRole
  ): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ ...userData, role }),
    });

    if (response.success && response.data) {
      this.storeTokens(response.data);
      return response.data;
    }

    throw new Error(response.message || 'Registration failed');
  }

  async verifyEmail(data: VerifyEmailRequest): Promise<void> {
    const response = await this.request<void>('/auth/verify-email', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error(response.message || 'Email verification failed');
    }
  }

  async sendOTP(email: string, type: 'password-reset' | 'email-verification'): Promise<void> {
    const response = await this.request<void>('/auth/send-otp', {
      method: 'POST',
      body: JSON.stringify({ email, type }),
    });

    if (!response.success) {
      throw new Error(response.message || 'Failed to send OTP');
    }
  }

  async verifyOTP(data: VerifyEmailRequest): Promise<void> {
    const response = await this.request<void>('/auth/verify-otp', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error(response.message || 'OTP verification failed');
    }
  }

  async resetPassword(data: ResetPasswordRequest & { resetToken: string }): Promise<void> {
    const response = await this.request<void>('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error(response.message || 'Password reset failed');
    }
  }

  async refreshToken(): Promise<AuthResponse> {
    const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.request<AuthResponse>('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });

    if (response.success && response.data) {
      this.storeTokens(response.data);
      return response.data;
    }

    throw new Error(response.message || 'Token refresh failed');
  }

  async logout(): Promise<void> {
    try {
      await this.request<void>('/auth/logout', {
        method: 'POST',
      });
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      this.clearTokens();
    }
  }

  // Forgot Password API methods
  async requestPasswordReset(email: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${this.baseUrl}/auth/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to send password reset OTP');
    }

    return {
      success: data.success,
      message: data.message || 'Password reset OTP sent successfully'
    };
  }

  async resetPasswordWithOTP(email: string, otp: string, newPassword: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${this.baseUrl}/auth/reset-password-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, otp, newPassword }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Password reset failed');
    }

    return {
      success: data.success,
      message: data.message || 'Password reset successful'
    };
  }

  async resendPasswordResetOTP(email: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${this.baseUrl}/auth/resend-password-reset-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to resend password reset OTP');
    }

    return {
      success: data.success,
      message: data.message || 'Password reset OTP sent successfully'
    };
  }

  getCurrentUser(): User | null {
    if (typeof window === 'undefined') return null;
    const userData = localStorage.getItem(STORAGE_KEYS.USER_DATA);
    return userData ? JSON.parse(userData) : null;
  }

  isAuthenticated(): boolean {
    return !!this.getStoredToken();
  }
}

export const authApiService = new AuthApiService();
