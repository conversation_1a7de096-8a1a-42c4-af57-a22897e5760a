'use client';

import { useState, useEffect, useCallback } from 'react';

interface UseResendTimerReturn {
  timeLeft: number;
  canResend: boolean;
  startTimer: (seconds?: number) => void;
  resetTimer: () => void;
}

export function useResendTimer(initialSeconds: number = 60): UseResendTimerReturn {
  const [timeLeft, setTimeLeft] = useState(0);
  const [canResend, setCanResend] = useState(true);

  const startTimer = useCallback((seconds: number = initialSeconds) => {
    setTimeLeft(seconds);
    setCanResend(false);
  }, [initialSeconds]);

  const resetTimer = useCallback(() => {
    setTimeLeft(0);
    setCanResend(true);
  }, []);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);

      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && !canResend) {
      setCanResend(true);
    }
  }, [timeLeft, canResend]);

  return {
    timeLeft,
    canResend,
    startTimer,
    resetTimer,
  };
}
