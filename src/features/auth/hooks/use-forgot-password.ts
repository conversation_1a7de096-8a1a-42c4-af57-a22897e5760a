'use client';

import { useState, useCallback } from 'react';
import { authApiService } from '../services/auth-api';

interface UseForgotPasswordReturn {
  // State
  isLoading: boolean;
  error: string | null;
  success: string | null;
  
  // Actions
  requestPasswordReset: (email: string) => Promise<void>;
  resetPasswordWithOTP: (email: string, otp: string, newPassword: string) => Promise<void>;
  resendPasswordResetOTP: (email: string) => Promise<void>;
  clearMessages: () => void;
}

export function useForgotPassword(): UseForgotPasswordReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const clearMessages = useCallback(() => {
    setError(null);
    setSuccess(null);
  }, []);

  const requestPasswordReset = useCallback(async (email: string) => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await authApiService.requestPasswordReset(email);
      setSuccess(response.message);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send password reset OTP');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const resetPasswordWithOTP = useCallback(async (email: string, otp: string, newPassword: string) => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await authApiService.resetPasswordWithOTP(email, otp, newPassword);
      setSuccess(response.message);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Password reset failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const resendPasswordResetOTP = useCallback(async (email: string) => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await authApiService.resendPasswordResetOTP(email);
      setSuccess(response.message);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to resend password reset OTP');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    isLoading,
    error,
    success,
    requestPasswordReset,
    resetPasswordWithOTP,
    resendPasswordResetOTP,
    clearMessages,
  };
}
