'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button, Input, Select } from '@/shared/components/ui';
import { useFormValidation, createEmailValidation, createPasswordValidation, createRequiredValidation } from '@/shared/hooks';
import { 
  BarberFormData
} from '@/types/auth';
import { ApiResponse,ApiError } from '@/types/api';
import Image from 'next/image';
import { authApiService } from '../services/auth-api';

const experienceOptions = [
  { value: '0-1', label: '0-1 years' },
  { value: '1-3', label: '1-3 years' },
  { value: '3-5', label: '3-5 years' },
  { value: '5-10', label: '5-10 years' },
  { value: '10-15', label: '10-15 years' },
  { value: '15-20', label: '15-20 years' },
  { value: '20+', label: '20+ years' },
];

export function SignupBarberForm() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState<string>('');

  // Use standardized form validation
  const { formState, setValue, handleSubmit, getFieldProps, setError } = useFormValidation<BarberFormData>({
    initialData: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      experience: '',
      vatNumber: '',
    },
    validationRules: {
      firstName: createRequiredValidation('First name'),
      lastName: createRequiredValidation('Last name'),
      email: createEmailValidation(),
      phone: createRequiredValidation('Phone number'),
      password: createPasswordValidation(),
      experience: createRequiredValidation('Experience'),
      vatNumber: createRequiredValidation('VAT number'),
    },
    onSubmit: async (data) => {
      setIsSubmitting(true);
      setApiError('');

      try {
        const result = await authApiService.register(data, 'barber');
        
        if ('requiresEmailVerification' in result) {
          router.push(`/auth/verify-email?type=barber&email=${encodeURIComponent(data.email)}`);
          return;
        }

        // Handle registration success
        router.push(`/auth/verify-email?type=barber&email=${encodeURIComponent(data.email)}`);

      } catch (error) {
        console.error('Registration error:', error);
        
        if (error instanceof Error) {
          try {
            const errorResult = JSON.parse(error.message);
            if (errorResult.details && Array.isArray(errorResult.details)) {
              errorResult.details.forEach((detail: any) => {
                const fieldName = detail.field as keyof BarberFormData;
                setError(fieldName, detail.error);
              });
            } else {
              // Change from errorResult.message to errorResult.error
              setApiError(errorResult.error || 'Registration failed');
            }
          } catch {
            // Change from error.message to check for error field
            setApiError(error.message || 'Registration failed');
          }
        } else {
          setApiError('Network error. Please try again.');
        }
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  const handleSelectChange = (field: keyof BarberFormData, value: string) => {
    setValue(field, value);
  };

  const handleGoogleSignUp = () => {
    console.log('Google sign-up clicked');
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 lg:space-y-5">
      {/* API Error Display */}
      {apiError && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
          {apiError}
        </div>
      )}

      {/* Name Fields */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 lg:gap-4">
        <Input
          type="text"
          name="firstName"
          placeholder="Enter your First Name"
          value={formState.data.firstName}
          onChange={(e) => setValue('firstName', e.target.value)}
          onBlur={() => getFieldProps('firstName').onBlur()}
          error={formState.errors.firstName}
          required
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          }
          label="First Name"
        />

        <Input
          type="text"
          name="lastName"
          placeholder="Enter your Last Name"
          value={formState.data.lastName}
          onChange={(e) => setValue('lastName', e.target.value)}
          onBlur={() => getFieldProps('lastName').onBlur()}
          error={formState.errors.lastName}
          required
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          }
          label="Last Name"
        />
      </div>

      {/* Email */}
      <Input
        type="email"
        name="email"
        placeholder="Enter your Email"
        value={formState.data.email}
        onChange={(e) => setValue('email', e.target.value)}
        onBlur={() => getFieldProps('email').onBlur()}
        error={formState.errors.email}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
          </svg>
        }
        label="Email"
      />

      {/* Phone Number */}
      <Input
        type="tel"
        name="phone"
        placeholder="Enter your Phone Number"
        value={formState.data.phone}
        onChange={(e) => setValue('phone', e.target.value)}
        onBlur={() => getFieldProps('phone').onBlur()}
        error={formState.errors.phone}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
          </svg>
        }
        label="Phone Number"
      />

      {/* Experience */}
      <Select
        options={experienceOptions}
        value={formState.data.experience}
        onChange={(value) => handleSelectChange('experience', value)}
        placeholder="Select"
        label="Experience"
        error={formState.errors.experience}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8" />
          </svg>
        }
      />

      {/* VAT Number */}
      <Input
        type="text"
        name="vatNumber"
        placeholder="Enter your VAT Number"
        value={formState.data.vatNumber}
        onChange={(e) => setValue('vatNumber', e.target.value)}
        onBlur={() => getFieldProps('vatNumber').onBlur()}
        error={formState.errors.vatNumber}
        required
        icon={
          <span className="text-gray-400 font-medium">#</span>
        }
        label="VAT Number"
      />

      {/* Password */}
      <Input
        type={showPassword ? 'text' : 'password'}
        name="password"
        placeholder="• • • • • • • •"
        value={formState.data.password}
        onChange={(e) => setValue('password', e.target.value)}
        onBlur={() => getFieldProps('password').onBlur()}
        error={formState.errors.password}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        }
        rightIcon={
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="hover:text-gray-600"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {showPassword ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              )}
            </svg>
          </button>
        }
        label="Password"
      />

      {/* Submit Button */}
      <Button
        type="submit"
        className="w-full"
        size="lg"
        disabled={isSubmitting || !formState.isValid}
      >
        {isSubmitting ? 'Registering...' : 'Register'}
      </Button>

      {/* Divider */}
      <div className="flex items-center my-6">
        <div className="flex-1 border-t border-gray-300"></div>
        <span className="px-4 text-sm text-gray-500">or Continue with</span>
        <div className="flex-1 border-t border-gray-300"></div>
      </div>

      {/* Google Sign Up */}
      <Button
        type="button"
        variant="outline"
        onClick={handleGoogleSignUp}
        className="w-full flex items-center justify-center gap-3"
        size="lg"
      >
        <Image
          src="/assets/images/google-logo.svg"
          alt="Google"
          width={20}
          height={20}
        />
        Sign up with Google
      </Button>

      {/* Login Link */}
      <div className="text-center">
        <span className="text-gray-600 text-sm">
          Already have an account?{' '}
          <button
            type="button"
            onClick={() => router.push('/auth/login')}
            className="text-amber-600 hover:text-amber-700 font-medium"
          >
            Login
          </button>
        </span>
      </div>
    </form>
  );
}
