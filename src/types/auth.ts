import { ApiResponse } from "./api";

// Authentication related types
export type UserRole = 'client' | 'barber' | 'shop-owner';
// Base user interface matching your backend response
export interface BaseUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  role: UserRole;
  status: string;
  languagePreference: string;
  profilePhoto?: string | null;
  isEmailVerified: boolean;
  emailOtpExpiresAt?: string | null;
  emailVerifiedAt?: string | null;
  otpAttempts: number;
  lastOtpRequestAt?: string | null;
  passwordResetOtpExpiresAt?: string | null;
  passwordResetAttempts: number;
  lastPasswordResetRequestAt?: string | null;
  lastLoginAt?: string | null;
  createdAt: string;
  updatedAt: string;
}

// Form Data Types (what frontend collects)
export interface ClientFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string; // Form field name
  password: string;
}

export interface BarberFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string; // Form field name
  password: string;
  experience: string;
  vatNumber: string;
}

export interface ShopOwnerFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string; // Form field name
  password: string;
  businessName: string;
  vatNumber: string;
}

// Full User Types (what backend returns)
export interface Client extends BaseUser {
  role: 'client';
  clientProfile?: {
    userId: string;
    locationId: string | null;
    createdAt: string;
    updatedAt: string;
  };
  barberProfile: null;
  shopOwnerProfile: null;
  adminProfile: null;
}

export interface Barber extends BaseUser {
  role: 'barber';
  barberProfile?: {
    userId: string;
    experience?: string;
    vatNumber?: string;
    createdAt: string;
    updatedAt: string;
  };
  clientProfile: null;
  shopOwnerProfile: null;
  adminProfile: null;
}

export interface ShopOwner extends BaseUser {
  role: 'shop-owner';
  shopOwnerProfile?: {
    userId: string;
    businessName?: string;
    vatNumber?: string;
    createdAt: string;
    updatedAt: string;
  };
  clientProfile: null;
  barberProfile: null;
  adminProfile: null;
}

// Union type for all users
export type User = Client | Barber | ShopOwner;

// Authentication state
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Authentication responses matching your backend
export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

// Login API response matching your backend
export interface LoginApiResponse extends ApiResponse<{
  user: User;
  requiresEmailVerification?: boolean;
  message: string;
}> {
  requiresEmailVerification?: boolean;
}

// Registration API response matching your backend
export interface RegisterApiResponse extends ApiResponse<{
  user: User;
  accessToken: string;
  refreshToken: string;
  message: string;
  otpSent: boolean;
}> {}

// Form Data Types
export interface LoginFormData {
  email: string;
  password: string;
}

export interface ResetPasswordRequest {
  password: string;
  confirmPassword: string;
}

export interface VerifyEmailRequest {
  email: string;
  otp: string; 
}

// Auth service request types (what we send to API)
export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber: string; // API expects this name
  role: 'client' | 'barber' | 'shop-owner';
  // Optional fields based on role
  experience?: string;
  vatNumber?: string;
  businessName?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface SendOTPRequest {
  email: string;
}