// UI Component types

// Button variants and sizes
export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
export type ButtonSize = 'sm' | 'md' | 'lg';

// Input types
export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

// Select option structure
export interface SelectOption {
  value: string;
  label: string;
}

// Select component props
export interface SelectProps {
  options: SelectOption[];
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  label?: string;
  required?: boolean;
  error?: string;
  className?: string;
  icon?: React.ReactNode;
}

// Tab structure
export interface Tab {
  id: string;
  label: string;
  icon?: React.ReactNode;
}

// Tab selector props
export interface TabSelectorProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

// Modal props
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children?: React.ReactNode;
  className?: string;
}

// Success modal specific props
export interface SuccessModalProps extends ModalProps {
  message?: string;
  buttonText?: string;
  onButtonClick?: () => void;
}

// Progress indicator props
export interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  className?: string;
}

// OTP Input props
export interface OTPInputProps {
  length?: number;
  onComplete?: (otp: string) => void;
  onChange?: (otp: string) => void;
  className?: string;
}

// File upload props
export interface FileUploadProps {
  label?: string;
  placeholder?: string;
  accept?: string;
  onChange?: (file: File | null) => void;
  className?: string;
  error?: string;
  required?: boolean;
}

// Multi-select tags props
export interface MultiSelectTagsProps {
  options: string[];
  selectedValues: string[];
  onChange: (values: string[]) => void;
  label?: string;
  placeholder?: string;
  allowCustom?: boolean;
  customLabel?: string;
  className?: string;
  variant?: 'default' | 'primary';
}

// Option button props
export interface OptionButtonProps {
  children: React.ReactNode;
  selected?: boolean;
  onClick?: () => void;
  variant?: 'default' | 'primary';
  className?: string;
}

// Auth layout props
export interface AuthLayoutProps {
  children: React.ReactNode;
  title?: string;
  showBackButton?: boolean;
  onBack?: () => void;
  className?: string;
  heroContent?: React.ReactNode;
  variant?: 'default' | 'home';
  contentType?: 'simple' | 'complex'; // simple = login/forgot, complex = signup with tabs
}
