version: 1
applications:
  - appRoot: .
    frontend:
      phases:
        preBuild:
          commands:
            - echo "Installing dependencies..."
            - npm ci
            - echo "Current Node.js version:"
            - node --version
            - echo "Current npm version:"
            - npm --version
        build:
          commands:
            - echo "Building Next.js application..."
            - npm run build
            - echo "Build completed successfully"
      artifacts:
        baseDirectory: .next
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
          - .next/cache/**/*
    buildSpec: |
      version: 0.1
      phases:
        preBuild:
          commands:
            # Set Node.js version (Amplify supports Node 18+)
            - nvm use 18
            - echo "Node version:" && node --version
            - echo "NPM version:" && npm --version
            
            # Install dependencies
            - echo "Installing dependencies..."
            - npm ci
            
            # Environment setup
            - echo "Setting up environment variables..."
            - |
              if [ -z "$NEXT_PUBLIC_API_URL" ]; then
                echo "Warning: NEXT_PUBLIC_API_URL not set, using default"
                export NEXT_PUBLIC_API_URL="https://your-api-domain.com/api/v1"
              fi
            
        build:
          commands:
            # TypeScript type checking
            - echo "Running TypeScript type checking..."
            - npx tsc --noEmit
            
            # ESLint checking
            - echo "Running ESLint..."
            - npm run lint
            
            # Build the application
            - echo "Building Next.js application..."
            - npm run build
            
            # Verify build output
            - echo "Verifying build output..."
            - ls -la .next/
            
        postBuild:
          commands:
            - echo "Build completed successfully!"
            - echo "Generated files:"
            - find .next -name "*.html" -o -name "*.js" -o -name "*.css" | head -20
      artifacts:
        baseDirectory: .next
        files:
          - '**/*'
        name: barber-booking-frontend-build
      cache:
        paths:
          - node_modules/**/*
          - .next/cache/**/*
          - ~/.npm/**/*